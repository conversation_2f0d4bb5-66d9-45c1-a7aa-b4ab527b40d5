dispatch:
  # Route brain.propbolt.com to backend (default service) - Admin Only
  - url: "brain.propbolt.com/*"
    service: default

  # Route api.propbolt.com to backend (default service) - User API
  - url: "api.propbolt.com/*"
    service: default

  # Route admin.propbolt.com to backend (default service) - Admin redirect target
  - url: "admin.propbolt.com/*"
    service: default

  # Route go.propbolt.com to backend (default service) - User redirect target
  - url: "go.propbolt.com/*"
    service: default

  # Route propbolt.com and www.propbolt.com to frontend service
  - url: "propbolt.com/*"
    service: frontend

  - url: "www.propbolt.com/*"
    service: frontend

  # Default routing for App Engine URLs
  - url: "*/api/*"
    service: default

  - url: "*/"
    service: frontend
