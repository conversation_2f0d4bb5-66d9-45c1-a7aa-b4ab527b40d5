package realestate

import (
	"fmt"
)

// InvoluntaryLiensRequest represents the request for involuntary liens information
type InvoluntaryLiensRequest struct {
	ID      string `json:"id,omitempty"`
	Address string `json:"address,omitempty"`
	House   string `json:"house,omitempty"`
	Street  string `json:"street,omitempty"`
	City    string `json:"city,omitempty"`
	State   string `json:"state,omitempty"`
	Zip     string `json:"zip,omitempty"`
}

// LienInfo represents information about a lien
type LienInfo struct {
	LienType        string  `json:"lienType"`
	LienAmount      float64 `json:"lienAmount"`
	LienDate        string  `json:"lienDate"`
	LienHolder      string  `json:"lienHolder"`
	DocumentNumber  string  `json:"documentNumber"`
	BookPage        string  `json:"bookPage"`
	Status          string  `json:"status"`
	ReleaseDate     string  `json:"releaseDate,omitempty"`
}

// TaxLienInfo represents tax lien specific information
type TaxLienInfo struct {
	TaxYear         int     `json:"taxYear"`
	TaxAmount       float64 `json:"taxAmount"`
	PenaltiesInterest float64 `json:"penaltiesInterest"`
	TotalOwed       float64 `json:"totalOwed"`
	Status          string  `json:"status"`
	SaleDate        string  `json:"saleDate,omitempty"`
}

// JudgmentInfo represents judgment information
type JudgmentInfo struct {
	JudgmentAmount  float64 `json:"judgmentAmount"`
	JudgmentDate    string  `json:"judgmentDate"`
	Plaintiff       string  `json:"plaintiff"`
	Defendant       string  `json:"defendant"`
	CaseNumber      string  `json:"caseNumber"`
	Court           string  `json:"court"`
	Status          string  `json:"status"`
}

// InvoluntaryLiensResponse represents the response from involuntary liens API
type InvoluntaryLiensResponse struct {
	PropertyID      string           `json:"propertyId"`
	Address         string           `json:"address"`
	TotalLiens      int              `json:"totalLiens"`
	TotalLienAmount float64          `json:"totalLienAmount"`
	Liens           []LienInfo       `json:"liens"`
	TaxLiens        []TaxLienInfo    `json:"taxLiens"`
	Judgments       []JudgmentInfo   `json:"judgments"`
	HasActiveLinks  bool             `json:"hasActiveLinks"`
	LastUpdated     string           `json:"lastUpdated"`
}

// InvoluntaryLiens fetches involuntary liens information for a property
func (c *Client) InvoluntaryLiens(request map[string]interface{}) (map[string]interface{}, error) {
	resp, err := c.MakeRequest("/v2/InvoluntaryLiens", request)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return result, nil
}
