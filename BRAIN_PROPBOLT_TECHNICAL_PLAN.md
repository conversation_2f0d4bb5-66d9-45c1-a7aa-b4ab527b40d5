# PropBolt Brain - Comprehensive Technical Plan
## Production-Grade Admin Platform for Vacant Land Analysis

### **Executive Summary**
`brain.propbolt.com` is a production-grade, admin-only platform for advanced vacant land search, analysis, and user management. This platform integrates with RealEstateAPI.com to provide comprehensive property data and analysis tools exclusively for administrators.

---

## **Phase 1: Infrastructure & Environment Setup**

### **1.1 Google Cloud Platform Infrastructure**

#### **Database Setup (Cloud SQL PostgreSQL)**
```bash
# Create GCP project
sudo gcloud projects create brain-propbolt-prod --name="PropBolt Brain Production"

# Set project
sudo gcloud config set project brain-propbolt-prod

# Enable required APIs
sudo gcloud services enable sqladmin.googleapis.com
sudo gcloud services enable run.googleapis.com
sudo gcloud services enable cloudbuild.googleapis.com
sudo gcloud services enable storage.googleapis.com
sudo gcloud services enable logging.googleapis.com

# Create Cloud SQL PostgreSQL instance
sudo gcloud sql instances create brain-propbolt-db \
    --database-version=POSTGRES_15 \
    --tier=db-custom-2-4096 \
    --region=us-east1 \
    --storage-type=SSD \
    --storage-size=100GB \
    --storage-auto-increase \
    --backup-start-time=02:00 \
    --enable-bin-log \
    --maintenance-window-day=SUN \
    --maintenance-window-hour=03 \
    --deletion-protection

# Create database
sudo gcloud sql databases create brain_propbolt_prod --instance=brain-propbolt-db

# Create database user
sudo gcloud sql users create brain_admin \
    --instance=brain-propbolt-db \
    --password=YOUR_SECURE_PASSWORD
```

#### **Cloud Storage Setup**
```bash
# Create storage bucket for assets
sudo gsutil mb -p brain-propbolt-prod -c STANDARD -l us-east1 gs://brain-propbolt-storage

# Set bucket permissions
sudo gsutil iam ch allUsers:objectViewer gs://brain-propbolt-storage
```

#### **Redis Cache Setup**
```bash
# Create Redis instance for caching
sudo gcloud redis instances create brain-propbolt-cache \
    --size=1 \
    --region=us-east1 \
    --redis-version=redis_6_x \
    --tier=basic
```

### **1.2 Technology Stack**
- **Database**: Google Cloud PostgreSQL
- **Authentication**: BetterAuth with role-based access (Admin only)
- **Maps**: Mapbox (with fallback to Google Maps Enterprise)
- **Backend**: GoLang API services
- **Frontend**: Next.js with TypeScript and Tailwind CSS
- **Deployment**: Google Cloud Platform (Cloud Run)
- **Monitoring**: LogRocket + Google Cloud Logging
- **Email**: Google Cloud Email API

---

## **Phase 2: Database Schema Design**

### **2.1 Core Tables**

#### **Users Table**
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'admin',
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **Properties Table**
```sql
CREATE TABLE properties (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(255) UNIQUE,
    address TEXT NOT NULL,
    city VARCHAR(255),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    lot_size_sqft INTEGER,
    zoning_type VARCHAR(100),
    property_type VARCHAR(100),
    price DECIMAL(12, 2),
    price_per_sqft DECIMAL(8, 2),
    days_on_market INTEGER,
    listing_status VARCHAR(50),
    has_utilities BOOLEAN DEFAULT FALSE,
    has_water BOOLEAN DEFAULT FALSE,
    has_sewer BOOLEAN DEFAULT FALSE,
    has_electric BOOLEAN DEFAULT FALSE,
    has_gas BOOLEAN DEFAULT FALSE,
    is_waterfront BOOLEAN DEFAULT FALSE,
    has_view BOOLEAN DEFAULT FALSE,
    is_buildable BOOLEAN DEFAULT FALSE,
    has_road_access BOOLEAN DEFAULT FALSE,
    flood_zone VARCHAR(50),
    property_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **Saved Properties Table**
```sql
CREATE TABLE saved_properties (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    category VARCHAR(100) DEFAULT 'general',
    notes TEXT,
    priority INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, property_id)
);
```

#### **Search History Table**
```sql
CREATE TABLE search_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    search_name VARCHAR(255),
    search_parameters JSONB NOT NULL,
    result_count INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **Analytics Table**
```sql
CREATE TABLE analytics (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **Notifications Table**
```sql
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## **Phase 3: RealEstateAPI.com Integration**

### **3.1 Required API Integrations**

#### **AutoComplete Address API**
- **Endpoint**: `POST https://api.realestateapi.com/v2/AutoComplete`
- **Purpose**: Address suggestions and location autocomplete
- **Input**: Partial address string
- **Output**: Structured address suggestions with types (A, C, G, N, P, T, Z)

#### **Mapping ("Pins") API**
- **Endpoint**: `POST https://api.realestateapi.com/v2/PropertyMapping`
- **Purpose**: Map pin locations for property visualization
- **Input**: Geographic bounds and search criteria
- **Output**: Property coordinates and basic info for map display

#### **Property Detail API**
- **Endpoint**: `POST https://api.realestateapi.com/v2/PropertyDetail`
- **Purpose**: Comprehensive property information
- **Input**: Address, property ID, or coordinates
- **Output**: Complete property data including owner info, history, comps, etc.

#### **Property Detail Bulk API**
- **Endpoint**: `POST https://api.realestateapi.com/v2/PropertyDetailBulk`
- **Purpose**: Bulk property data retrieval
- **Input**: Array of property IDs (up to 1000)
- **Output**: Complete property data for multiple properties

#### **Property Search API**
- **Endpoint**: `POST https://api.realestateapi.com/v2/PropertySearch`
- **Purpose**: Advanced property search with filters
- **Input**: Geographic bounds, filters, search criteria
- **Output**: Property list with detailed filtering options

#### **Involuntary Liens API**
- **Endpoint**: `POST https://api.realestateapi.com/v2/InvoluntaryLiens`
- **Purpose**: Lien information for properties
- **Input**: Property address or ID
- **Output**: Lien details and legal information

#### **PropertyComps v3 API**
- **Endpoint**: `POST https://api.realestateapi.com/v3/PropertyComps`
- **Purpose**: Advanced comparable properties analysis
- **Input**: Subject property with custom parameters
- **Output**: Comparable properties with valuation range

#### **PropertyComps v2 API**
- **Endpoint**: `POST https://api.realestateapi.com/v2/PropertyComps`
- **Purpose**: Basic comparable properties
- **Input**: Property address
- **Output**: Similar properties with sale data

#### **SkipTrace API**
- **Endpoint**: `POST https://api.realestateapi.com/v1/SkipTrace`
- **Purpose**: Owner contact information
- **Input**: Property address and owner name
- **Output**: Contact details, demographics, social profiles

---

## **Phase 4: Backend API Development (GoLang)**

### **4.1 Proxy Layer Architecture**
Create secure proxy endpoints that map 1:1 with RealEstateAPI.com endpoints:

```go
// Internal proxy endpoints for brain.propbolt.com
/api/v1/proxy/autocomplete          -> RealEstateAPI AutoComplete
/api/v1/proxy/mapping               -> RealEstateAPI Mapping
/api/v1/proxy/property-detail       -> RealEstateAPI Property Detail
/api/v1/proxy/property-detail-bulk  -> RealEstateAPI Property Detail Bulk
/api/v1/proxy/property-search       -> RealEstateAPI Property Search
/api/v1/proxy/involuntary-liens     -> RealEstateAPI Involuntary Liens
/api/v1/proxy/property-comps-v3     -> RealEstateAPI PropertyComps v3
/api/v1/proxy/property-comps-v2     -> RealEstateAPI PropertyComps v2
/api/v1/proxy/skiptrace             -> RealEstateAPI SkipTrace
```

### **4.2 Enhanced Internal Endpoints**
```go
// Enhanced internal endpoints
GET  /api/v1/properties             // List properties with filters
POST /api/v1/properties/search      // Advanced search with caching
GET  /api/v1/properties/:id         // Get property details
POST /api/v1/properties/:id/save    // Save property to watchlist
GET  /api/v1/saved-properties       // Get user's saved properties
POST /api/v1/search/save            // Save search parameters
GET  /api/v1/search/history         // Get search history
GET  /api/v1/analytics/dashboard    // Dashboard analytics
POST /api/v1/notifications          // Create notifications
GET  /api/v1/notifications          // Get user notifications
```

---

## **Phase 5: Frontend Application (Next.js)**

### **5.1 Core Pages Structure**

#### **Dashboard (`/dashboard`)**
- Welcome section with user name and date
- Active searches (saved parameters)
- Latest listings (24hr, 30-day, 90+ day)
- High equity deals section
- Watch list preview
- Market pulse indicators

#### **Property Search (`/search`)**
- Interactive Mapbox integration
- Advanced filtering panel with vacant land specific filters
- Drawing tools for custom area selection
- Results grid with sorting/filtering
- Save search functionality
- Bulk export options (CSV, PDF, email)

#### **Property Details (`/property/:id`)**
- Complete property information display
- Zoning analysis and permitted uses
- Habitability assessment scoring
- Distance metrics to key locations
- Commercial potential scoring
- Comparable properties analysis
- Historical price data and trends
- Add to watch list functionality

#### **Commercial Opportunity Analyzer (`/analyzer`)**
- Business type compatibility scoring
- Traffic analysis tools
- Demographic overlays
- Competition mapping
- Development cost estimator
- ROI projection tools

#### **Watch List Manager (`/watchlist`)**
- Grid/list view options
- Custom categorization
- Status tracking
- Bulk operations
- Comparison tools
- Notification settings

#### **Market Analytics (`/analytics`)**
- Trend dashboards with interactive charts
- Price density heatmaps
- Inventory level tracking
- Seasonal pattern analysis
- ML-based price predictions

#### **User Management (`/users`)**
- User profiles and permissions
- Activity logs and audit trails
- System usage analytics

#### **System Configuration (`/config`)**
- API endpoint configuration and testing
- Proxy management and health monitoring
- Data refresh controls
- Usage analytics and quotas

---

## **Phase 6: Deployment & Infrastructure**

### **6.1 Cloud Run Deployment**
```bash
# Build and deploy backend
sudo gcloud builds submit --tag gcr.io/brain-propbolt-prod/brain-api
sudo gcloud run deploy brain-api \
    --image gcr.io/brain-propbolt-prod/brain-api \
    --platform managed \
    --region us-east1 \
    --allow-unauthenticated \
    --set-env-vars="$(cat .env | grep -v '^#' | xargs)"

# Build and deploy frontend
sudo gcloud builds submit --tag gcr.io/brain-propbolt-prod/brain-frontend
sudo gcloud run deploy brain-frontend \
    --image gcr.io/brain-propbolt-prod/brain-frontend \
    --platform managed \
    --region us-east1 \
    --allow-unauthenticated
```

### **6.2 Domain Configuration**
```bash
# Configure custom domain
sudo gcloud run domain-mappings create \
    --service brain-frontend \
    --domain brain.propbolt.com \
    --region us-east1
```

### **6.3 CI/CD Pipeline**
```yaml
# cloudbuild.yaml
steps:
  - name: 'gcr.io/cloud-builders/go'
    args: ['build', '-o', 'brain-api', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/brain-api', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/brain-api']
  - name: 'gcr.io/cloud-builders/gcloud'
    args: ['run', 'deploy', 'brain-api', '--image', 'gcr.io/$PROJECT_ID/brain-api', '--region', 'us-east1']
```

---

## **Phase 7: Security & Monitoring**

### **7.1 Authentication Flow**
1. User accesses `brain.propbolt.com`
2. Redirect to `propbolt.com/login` for BetterAuth
3. Verify 'Admin' role requirement
4. Generate JWT token for internal API access
5. Redirect back to `brain.propbolt.com` with session

### **7.2 Monitoring Setup**
```bash
# Enable monitoring
sudo gcloud services enable monitoring.googleapis.com
sudo gcloud services enable logging.googleapis.com

# Create alerting policies
sudo gcloud alpha monitoring policies create --policy-from-file=monitoring-policy.yaml
```

---

## **Phase 8: Testing & Quality Assurance**

### **8.1 Testing Strategy**
- Unit tests for all API endpoints
- Integration tests for RealEstateAPI.com proxy layer
- End-to-end tests for critical user flows
- Performance testing for map rendering and search
- Security testing for authentication and authorization

### **8.2 Performance Optimization**
- Redis caching for API responses
- CDN for static assets
- Database query optimization
- Lazy loading for map components
- Image optimization and compression

---

## **Next Steps**

1. **Environment Setup**: Configure GCP infrastructure and services
2. **Database Migration**: Create and populate database schema
3. **API Integration**: Implement RealEstateAPI.com proxy layer
4. **Frontend Development**: Build Next.js application with core pages
5. **Authentication**: Integrate BetterAuth for admin access
6. **Testing**: Comprehensive testing across all components
7. **Deployment**: Production deployment with monitoring
8. **Documentation**: Complete API documentation and user guides

This plan provides a comprehensive roadmap for building `brain.propbolt.com` as a production-grade, admin-only platform for vacant land analysis and management.
